import { dataSourceFactory } from '@/domain/services/Rt/Firestore';
import { lockableDatSourceMapper } from '@/domain/services/Collaboration/LockableLogic';
import { mutableDatSourceMapper } from '@/domain/services/Collaboration/MutableLogic';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { collaborationUIBus, CollaborationUISignals } from '@/domain/signals/Collaboration';
import dataSources, {
	cacheDataSource,
	collectionMapper,
	Collections,
	DataSource,
	getCachedDataSource,
	removeCacheDataSource,
} from '@/domain/services/Rt/DataSource';

vi.mock('@/domain/services/Rt/Firestore', () => ({
	dataSourceFactory: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/LockableLogic', () => ({
	lockableDatSourceMapper: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/MutableLogic', () => ({
	mutableDatSourceMapper: vi.fn(),
}));

describe('DataSource', () => {
	const mutableDatSourceMapperForUser = vi.fn();
	const lockableDatSourceMapperForUser = vi.fn();

	beforeEach(() => {
		vi.resetAllMocks();
		(mutableDatSourceMapper as Mock).mockReturnValue(mutableDatSourceMapperForUser);
		(lockableDatSourceMapper as Mock).mockReturnValue(lockableDatSourceMapperForUser);
	});

	it('boots lockables datasource', () => {
		dataSources.locks('formSlug', 'submittableSlug', 'lockableId', 'consumer');
		expect(dataSourceFactory).toHaveBeenCalledWith(
			'locks-lockables-formSlug-submittableSlug-lockableId',
			'lockables',
			'formSlug-submittableSlug-lockableId',
			lockableDatSourceMapperForUser
		);
	});

	it('boots collaborators datasource', () => {
		dataSources.collaborators('formSlug', 'submittableSlug');
		expect(dataSourceFactory).toHaveBeenCalledWith(
			'collaborators-formSlug-submittableSlug',
			'collaborators',
			'formSlug-submittableSlug',
			collectionMapper
		);
	});

	it('boots generic collection datasource', () => {
		dataSources.collection('formSlug', 'submittableSlug', 'collectionId' as Collections);
		expect(dataSourceFactory).toHaveBeenCalledWith(
			'collectionId-formSlug-submittableSlug',
			'collectionId',
			'formSlug-submittableSlug',
			collectionMapper
		);
	});

	it('boots generic document datasource', () => {
		dataSources.document('formSlug', 'submittableSlug', 'collectionId' as Collections);
		expect(dataSourceFactory).toHaveBeenCalledWith(
			'collectionId-formSlug-submittableSlug',
			'collectionId',
			'formSlug-submittableSlug',
			{}
		);
	});

	it('maps data into collection', () => {
		expect(
			collectionMapper.extractData({
				'0': 'a',
				'1': 'b',
				'2': 'c',
			})
		).toStrictEqual(['a', 'b', 'c']);

		expect(collectionMapper.extractData([2, 3, 4])).toStrictEqual([2, 3, 4]);
		expect(collectionMapper.extractData(null)).toStrictEqual([]);

		expect(() => collectionMapper.extractData('foo-bar')).toThrowError('Invalid collection data: "foo-bar"');
		expect(() =>
			collectionMapper.extractData({
				'0': 'a',
				'1': 'b',
				'3': 'c',
			})
		).toThrowError('Invalid collection data: {"0":"a","1":"b","3":"c"}');
	});

	it('boots lockables datasource', () => {
		dataSources.lockables('formSlug', 'submittableSlug', 'lockableId', 'consumer');

		expect(dataSourceFactory).toHaveBeenCalledWith(
			'lockables-lockables-formSlug-submittableSlug-lockableId',
			'lockables',
			'formSlug-submittableSlug-lockableId',
			mutableDatSourceMapperForUser,
			false
		);

		expect(mutableDatSourceMapper).toHaveBeenCalledWith('consumer');
	});

	it('caches datasources', () => {
		const foo = {
			id: 'foo',
			destroy: vi.fn(),
		} as DataSource;

		const bar = {
			id: 'bar',
			destroy: vi.fn(),
		} as DataSource;

		cacheDataSource('foo', foo);
		cacheDataSource('bar', bar);

		expect(getCachedDataSource('foo').id).toBe('foo');
		expect(getCachedDataSource('bar').id).toBe('bar');
		expect(getCachedDataSource('baz')).toBeNull();

		collaborationUIBus.emit(CollaborationUISignals.RELOAD, undefined);

		expect(foo.destroy).toHaveBeenCalled();
		expect(bar.destroy).toHaveBeenCalled();

		expect(getCachedDataSource('foo')).toBeNull();
		expect(getCachedDataSource('bar')).toBeNull();
		expect(getCachedDataSource('baz')).toBeNull();
	});

	it('get data source cache', () => {
		cacheDataSource('fake-source-id', {
			id: 'fake-source-id',
			set: vi.fn(),
			get: vi.fn(),
			remove: vi.fn(),
			subscribe: vi.fn(),
			destroy: () => vi.fn(),
		});

		const data = getCachedDataSource('fake-source-id');
		expect(data).is.not.null;
		expect(data.id).is.equal('fake-source-id');
	});

	it('remove data source cache', () => {
		cacheDataSource('fake-source-id', {
			id: 'fake-source-id',
			set: vi.fn(),
			get: vi.fn(),
			remove: vi.fn(),
			subscribe: vi.fn(),
			destroy: () => vi.fn(),
		});

		expect(getCachedDataSource('fake-source-id')).is.not.null;
		removeCacheDataSource('fake-source-id');
		expect(getCachedDataSource('fake-source-id')).is.null;
	});
});
