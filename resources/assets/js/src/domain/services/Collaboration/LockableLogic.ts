import { User } from '@/domain/models/User';
import { guardForUser, LockableData } from '@/domain/services/Collaboration/MutableLogic';

type Lock = {
	user: string;
	deadline: number;
};

const lockableDatSourceMapper = (consumer: User) => ({
	extractData: (lockable: LockableData | null) => lockable?.lock || null,
	prepareData: (lockable: LockableData, lock: Lock) => {
		if (lock?.user && (lockable?.value as { sender?: string })?.sender) {
			(lockable.value as { sender: string }).sender = lock.user;
		}

		return { ...lockable, lock };
	},
  guard: guardForUser(consumer),
	rawProps: ['lock'] as (keyof LockableData)[],
});

// eslint-disable-next-line @typescript-eslint/naming-convention
const DEADLINE = 1000 * 15;

const isExpired = (data: Lock) => data.deadline + DEADLINE < new Date().getTime();

const isLock = (data: unknown): data is Lock => {
	if (typeof data !== 'object' || data === null) {
		return false;
	}

	return 'user' in data && 'deadline' in data && typeof data.user === 'string' && typeof data.deadline === 'number';
};

const isLocked = (data?: Lock | null) => isLock(data) && !isExpired(data);

const belongsToUser = (consumer: User) => (data?: Lock | null) =>
	data ? isLocked(data) && data.user === consumer.slug : false;

export { isLocked, isExpired, isLock, belongsToUser, Lock, DEADLINE, lockableDatSourceMapper };
