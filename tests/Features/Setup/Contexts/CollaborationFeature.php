<?php

namespace Features\Setup\Contexts;

use Arr;
use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Accounts\Services\MembershipService;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Events\CollaboratorWasInvited;
use AwardForce\Modules\Forms\Collaboration\Exceptions\GrantReportOwnerCannotBeChanged;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Collaboration\Services\Collaborators;
use AwardForce\Modules\Forms\Collaboration\ValueObjects\Privilege;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Notifications\Events\SystemEventListener;
use Faker\Factory as Faker;
use Google\Cloud\Core\Timestamp;
use Illuminate\Support\Facades\Event;
use Mockery as m;

trait CollaborationFeature
{
    private string $message;

    /**
     * @Given I have a submittable
     */
    public function iHaveASubmittable(): void
    {
        $this->submittable = $this->muffin(Entry::class, ['user_id' => $this->user->id]);
    }

    /**
     * @Given There is a submittable
     */
    public function thereIsASubmittable(): void
    {
        $this->submittable = $this->muffin(Entry::class, ['user_id' => ($this->muffin(User::class))->id]);
    }

    /**
     * @Given I have a submittable grant report
     */
    public function iHaveASubmittableGrantReport(): void
    {
        $entry = $this->muffin(Entry::class, ['user_id' => $this->user->id]);
        $this->submittable = $this->muffin(GrantReport::class, ['entry_id' => $entry->id]);
    }

    /**
     * @Given there are :count collaborators for that submittable with privilege :privilege
     */
    public function thereAreCollaboratorsForThatSubmittableWithPrivilege(int $count = 2, string $privilege = 'editor'): void
    {
        $this->collaborators = collect(range(1, $count))->map(function () use ($privilege) {
            $this->muffin(Membership::class, ['user_id' => ($user = $this->muffin(User::class))->id]);
            $collaborator = $this->muffin(Collaborator::class, [
                'submittable_id' => $this->submittable->id,
                'submittable_type' => $this->submittable->getMorphClass(),
                'user_id' => $user->id,
                'privilege' => Privilege::from($privilege),
            ]);

            return $collaborator;
        });
    }

    /**
     * @Given I have an invited collaborator with privilege :privilege
     */
    public function iHaveAnInvitedCollaboratorWithPrivilege(string $privilege): void
    {
        $this->muffin(Membership::class, ['user_id' => ($user = $this->muffin(User::class))->id]);
        $this->invitedCollaborator = $this->muffin(Collaborator::class, [
            'submittable_id' => $this->submittable->id,
            'submittable_type' => $this->submittable->getMorphClass(),
            'user_id' => $user->id,
            'privilege' => Privilege::from($privilege),
        ]);
    }

    /**
     * @Given I invite a collaborator with privilege :privilege
     */
    public function iInviteACollaboratorWithPrivilege(string $privilege = 'editor'): void
    {
        $this->mockInvitationRequest();

        $this->muffin(Membership::class, ['user_id' => ($this->invitedUser = $this->muffin(User::class, ['email' => $email = Faker::create()->email]))->id]);

        $this->response = $this->route('POST', 'collaborators.invite', [
            'form' => (string) $this->submittable->getForm()->slug,
            'submittableSlug' => (string) $this->submittable->slug,
        ], [
            'emails' => [$email],
            'message' => $this->message = Faker::create()->sentence,
            'privilege' => $privilege,
        ]);
    }

    private function mockInvitationRequest(): void
    {
        $membershipServiceMock = m::mock(MembershipService::class);
        $membershipServiceMock->shouldReceive('registerMembership')->withAnyArgs()->once()->andReturnNull();
        app()->instance(MembershipService::class, $membershipServiceMock);

        $systemEventListener = m::mock(SystemEventListener::class);
        $systemEventListener->shouldReceive('whenCollaboratorWasCreated')->byDefault()->andReturnNull();
        $systemEventListener->shouldReceive('whenRoleWasGranted')->byDefault()->andReturnNull();
        app()->instance(SystemEventListener::class, $systemEventListener);

        Event::fake([CollaboratorWasInvited::class]);
    }

    /**
     * @Given I re invite a collaborator
     */
    public function iReInviteACollaborator(): void
    {
        $this->mockInvitationRequest();

        $this->response = $this->route('POST', 'collaborators.reinvite', [
            'collaborator' => (string) $this->invitedCollaborator->slug,
            'message' => $this->message = Faker::create()->sentence,
        ]);
    }

    /**
     * @Then I fetch collaborators
     */
    public function iFetchEntryCollaborators(): void
    {
        $this->response = $this->route('GET', 'collaborators.index', [
            'form' => (string) $this->submittable->getForm()->slug,
            'submittableSlug' => (string) $this->submittable->slug,
        ]);
    }

    /**
     * @Then I should get :count collaborators
     */
    public function iShouldGetCollaborators(int $count = 2): void
    {
        $this->assertResponseOk();
        $this->response->assertJsonCount($count, 'collaborators');
    }

    /**
     * @Then I should see an empty list of collaborators
     */
    public function iShouldSeeAnEmptyListOfCollaborators(): void
    {
        $this->assertResponseOk();
        $this->response->assertJsonCount(0, 'collaborators');
    }

    /**
     * @Then I should see an exception that the ownership can not be transferred for grant reports
     */
    public function iShouldSeeAnExceptionThatTheOwnershipCanNotBeTransferredForGrantReports(): void
    {
        $this->assertTrue($this->response->exception instanceof GrantReportOwnerCannotBeChanged);
    }

    /**
     * @Then I update collaborator privilege to :privilege
     */
    public function iUpdateCollaboratorPrivilege(string $privilege): void
    {
        $this->response = $this->route('PUT', 'collaborators.privilege.update', [
            'collaborator' => (string) $this->collaborators->first()->slug,
        ], [
            'privilege' => $privilege,
        ]);
    }

    /**
     * @Then I delete a collaborator
     */
    public function iDeleteACollaborator(): void
    {
        $this->response = $this->route('DELETE', 'collaborators.delete', [
            'collaborator' => (string) $this->collaborators->first()->slug,
        ]);
    }

    /**
     * @Then I should see collaborator with updated privilege :privilege
     */
    public function iShouldSeeCollaboratorWithUpdatePrivilege(string $privilege): void
    {
        $collaborator = collect($this->response->json('collaborators'))->where('owner', '=', false)->first();
        $this->assertTrue($privilege === Arr::get($collaborator, 'privilege'));
    }

    /**
     * @Then there should be :collaboratorsCount collaborators for that submittable with privilege :privilege
     */
    public function thereShouldBeCollaboratorsForThatSubmittableWithPrivilege(int $collaboratorsCount, string $privilege): void
    {
        $this->assertTrue($collaboratorsCount === collect($this->response->json('collaborators'))->where('privilege', $privilege)->count());
    }

    /**
     * @Then invitation should have been sent
     */
    public function invitationShouldHaveBeenSent(): void
    {
        Event::assertDispatched(CollaboratorWasInvited::class, function (CollaboratorWasInvited $event) {
            $this->assertSame($this->message, $event->message);

            return true;
        });
    }

    /**
     * @When I transfer ownership to a new user
     */
    public function iTransferOwnershipToACollaborator(): void
    {
        $this->response = $this->route('POST', 'collaborators.owner', [
            'form' => (string) $this->submittable->getForm()->slug,
            'submittableSlug' => (string) $this->submittable->slug,
            'user' => (string) ($this->newOwner = $this->muffin(User::class))->slug,
        ]);
    }

    /**
     * @Then I should see the ownership transferred
     */
    public function iShouldSeeTheOwnershipTransferred(): void
    {
        $this->assertTrue($this->submittable->fresh()->getUserId() === $this->newOwner->id);
    }

    /**
     * @Given I am a collaborator for the submittable
     */
    public function iAmACollaboratorForTheSubmittable(): void
    {
        app(Collaborators::class)->addWithoutInvitation(
            $this->user,
            $this->submittable,
            Privilege::from('editor')
        );
    }

    /**
     * @Then I should see the submittable
     */
    public function iShouldSeeTheSubmittable(): void
    {
        $this->response = $this->route('GET', 'entry.entrant.index');

        $this->assertResponseOk();
        $this->assertResponseContains($this->submittable->slug);
    }

    /**
     * @Given Enable form collaborative
     */
    public function enableFormCollaborative(): void
    {
        $form = FormSelector::get();
        $form->settings = FormSettings::create(['collaborative' => true]);
        $form->save();
    }

    /**
     * @Then I should be able to access the submittable
     */
    public function iShouldBeAbleToAccessTheSubmittable(): void
    {
        $databaseMock = m::mock(Database::class);
        $databaseMock->shouldReceive('expireAt')->once()->andReturn(new Timestamp(new \DateTime()));
        $databaseMock->shouldReceive('generateAuthToken')->once()->andReturn('');
        app()->instance(Database::class, $databaseMock);

        $this->route('GET', 'entry-form.entrant.edit', ['entry' => $this->submittable->slug]);

        $this->assertResponseOk();
    }
}
