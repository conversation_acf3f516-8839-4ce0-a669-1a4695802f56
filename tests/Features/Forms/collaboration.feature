Feature: Collaboration

  Background:
    Given the collaboration feature is enabled
    And I am currently logged in as an "Entrant"
    And there is an active season
    And A form exists
    And I have confirmed my account
    And there is an active entry round
    And there is a Details tab setup

  Scenario: Can get collaborators for a submittable
    Given I am currently logged in as an "Entrant"
    And I have a submittable
    And there are 2 collaborators for that submittable with privilege "editor"
    When I fetch collaborators
    Then I should get 3 collaborators

  Sc<PERSON>rio: Can not get collaborators for a submittable if collaboration feature is disabled
    Given I am currently logged in as an "Entrant"
    And I have a submittable
    And the collaboration feature is disabled
    And there are 2 collaborators for that submittable with privilege "viewer"
    When I fetch collaborators
    Then I should be redirected to feature collaboration disabled page

  Scenario: Can not get collaborators for a submittable if user is not owner or collaborator
    Given I am currently logged in as an "Entrant"
    And There is a submittable
    And there are 2 collaborators for that submittable with privilege "viewer"
    When I fetch collaborators
    Then I should receive unauthorised error

  Scenario: Can invite a collaborator
    Given I am currently logged in as an "Entrant"
    And I have a submittable
    And there are 2 collaborators for that submittable with privilege "viewer"
    When I invite a collaborator with privilege "viewer"
    Then there should be 3 collaborators for that submittable with privilege "viewer"
    And invitation should have been sent

  Sc<PERSON>rio: Can not invite collaborators for a submittable if user is not manager or owner
    Given I am currently logged in as an "Entrant"
    And There is a submittable
    When I invite a collaborator with privilege "viewer"
    Then I should receive unauthorised error

  Scenario: Can re-invite a collaborator
    Given I am currently logged in as an "Entrant"
    And I have a submittable
    And I have an invited collaborator with privilege "viewer"
    When I re invite a collaborator
    Then I should see collaborator with updated privilege "viewer"
    And invitation should have been sent

  Scenario: Can not re-invite a collaborator if user is not manager or owner
    Given I am currently logged in as an "Entrant"
    And There is a submittable
    And I have an invited collaborator with privilege "viewer"
    When I re invite a collaborator
    Then I should receive unauthorised error

  Scenario: Can update a collaborator's privilege
    Given I am currently logged in as an "Entrant"
    And I have a submittable
    And there are 1 collaborators for that submittable with privilege "viewer"
    When I update collaborator privilege to "submitter"
    Then I should see collaborator with updated privilege "submitter"

  Scenario: Can not update a collaborator's privilege if user is not manager or owner
    Given I am currently logged in as an "Entrant"
    And There is a submittable
    And there are 1 collaborators for that submittable with privilege "viewer"
    When I update collaborator privilege to "submitter"
    Then I should receive unauthorised error

  Scenario: Can transfer an entry ownership to a collaborator
    Given I am currently logged in as an "Entrant"
    And I have a submittable
    When I transfer ownership to a new user
    Then I should see the ownership transferred

  Scenario: Can not transfer a grant report ownership to a collaborator
    Given I am currently logged in as an "Entrant"
    And I have a submittable grant report
    When I transfer ownership to a new user
    Then I should see an exception that the ownership can not be transferred for grant reports

  Scenario: Can not transfer an entry ownership to a collaborator if user is not manager or owner
    Given I am currently logged in as an "Entrant"
    And There is a submittable
    When I transfer ownership to a new user
    Then I should receive unauthorised error

  Scenario: Can delete a collaborator
    Given I am currently logged in as an "Entrant"
    And I have a submittable
    And there are 2 collaborators for that submittable with privilege "viewer"
    When I delete a collaborator
    Then I should get 2 collaborators

  Scenario: Can not delete a collaborator if user is not manager or owner
    Given I am currently logged in as an "Entrant"
    And There is a submittable
    And there are 1 collaborators for that submittable with privilege "viewer"
    When I delete a collaborator
    Then I should receive unauthorised error

  Scenario: Can view entries where user is a collaborator
    Given I am currently logged in as an "Entrant"
    And Enable form collaborative
    And There is a submittable
    And I am a collaborator for the submittable
    Then I should see the submittable
    And I should be able to access the submittable
