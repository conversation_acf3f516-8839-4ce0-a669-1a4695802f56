{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "repositories": [{"type": "path", "url": "../platform", "canonical": false}, {"type": "git", "url": "**************:tectonic/platform.git"}, {"type": "path", "url": "../awardforce-translations", "canonical": false}, {"type": "git", "url": "**************:tectonic/awardforce-translations.git"}, {"type": "path", "url": "../awardforce-icons", "canonical": false}, {"type": "git", "url": "**************:tectonic/awardforce-icons.git"}, {"type": "git", "url": "https://github.com/tectonic/omnipay-bluepay"}, {"type": "git", "url": "https://github.com/tectonic/omnipay-netbanx"}, {"type": "git", "url": "https://github.com/tectonic/omnipay-realex"}, {"type": "git", "url": "https://github.com/tectonic/omnipay-sips2"}, {"type": "vcs", "url": "https://github.com/valorin/api-library-php5"}, {"type": "git", "url": "**************:tectonic/laravel-localisation.git"}, {"type": "git", "url": "**************:tectonic/copyleaks.git"}, {"type": "git", "url": "**************:tectonic/laravel-url-shortener.git"}, {"type": "vcs", "url": "https://github.com/MWL91/Behat-Laravel-Extension"}, {"type": "git", "url": "**************:tectonic/elasticsearch.git"}, {"type": "git", "url": "**************:tectonic/lowrapper.git"}, {"type": "git", "url": "**************:kontoulis/identifier.git"}], "require": {"php": "^8.3", "ext-dom": "*", "ext-intl": "*", "ext-libxml": "*", "ext-zip": "*", "ambroisemaupate/omnipay-sips2": "3.0.0", "api-ecosystem-for-laravel/dingo-api": "^4.2.2", "awardforce/icons": "^1.3.5", "awardforce/platform": "^13.0.34", "aws/aws-sdk-php": "^3.24", "basemkhirat/elasticsearch": "dev-master#a09e377d6d560ffdf33afdcf477fef6cd6c69429", "bilfeldt/laravel-http-client-logger": "^2.2", "brick/postcode": "^0.2.7", "chargebee/chargebee-php": "^4.4", "creativeforce/translations": "^2.5.22", "curl/curl": "~1.2.0", "cybersource/sdk-php": "1.0.2", "digitickets/omnipay-realex": "6.0.0", "dompdf/dompdf": "^2.0.7", "embed/embed": "^4.4", "emergingdzns/omnipay-bluepay": "2.0.6", "endroid/qr-code": "^4.8.1", "eventsauce/eventsauce": "^3.6", "eventsauce/message-repository-for-illuminate": "^1.2", "fedeisas/laravel-mail-css-inliner": "^5.3", "firebase/php-jwt": "^6.3", "gazsp/baum": "^2.0", "google/cloud-firestore": "^1.41", "ibericode/vat": "^2.0", "imgix/imgix-php": "^4.1", "instasent/sms-counter-php": "^0.5.2", "justinrainbow/json-schema": "^5.2", "kreait/laravel-firebase": "^5.7", "laracrafts/laravel-url-shortener": "dev-feature/laravel-9", "laravel/browser-kit-testing": "^7.1", "laravel/framework": "^11.0", "laravel/helpers": "^1.6", "laravel/horizon": "^5.18", "laravel/slack-notification-channel": "^3.2", "laravel/socialite": "^5.6", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1.62", "mailgun/mailgun-php": "^3.6.3", "mateusjunges/laravel-kafka": "^2.7", "matthiasmullie/minify": "^1.3", "mheap/phpunit-github-actions-printer": "^1.5", "mnvx/lowrapper": "dev-master", "omnipay/authorizenet": "^3.0", "omnipay/common": "^3.3", "omnipay/eway": "^3.0", "omnipay/netbanx": "3.0.0", "omnipay/payflow": "^3.0", "omnipay/paypal": "^3.0", "omnipay/sagepay": "^4.0", "omnipay/securepay": "^3.0", "omnipay/stripe": "dev-master#de12e273338cb3b8e38e4041da6b80b9ff7ce0af", "omnipay/worldpay": "^3.0", "onelogin/php-saml": "^3.1", "optimalpayments/optimal-php-sdk": "^1.0", "php-http/guzzle7-adapter": "^1.0.0", "php-http/message-factory": "^1.1", "phpseclib/phpseclib": "3.0.36", "predis/predis": "^1.1", "prism-php/bedrock": "^1.3", "propaganistas/laravel-phone": "^5.3", "pusher/pusher-php-server": "^7.2", "ramsey/identifier": "dev-feature/brick-math-compatibility", "spatie/browsershot": "^5", "spatie/laravel-webhook-server": "^3.4", "spatie/simple-excel": "^3.6", "spomky-labs/otphp": "v7.0.4", "stripe/stripe-php": "^15.8", "symfony/dependency-injection": "^6.0", "symfony/http-client": "^7.0", "symfony/mailgun-mailer": "^7.0", "symfony/string": "^6.4|^7.0", "tectonic/localisation": "~3.0.2", "tijsverkoyen/css-to-inline-styles": "^2.2", "twilio/sdk": "^8.3.13", "vonage/client": "^4.0", "vxm/laravel-async": "^5.0", "zumba/amplitude-php": "^2.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "barryvdh/laravel-ide-helper": "^3", "behat/behat": "^3.14", "behat/mink": "^1.8", "beyondcode/laravel-dump-server": "^2.0", "brianium/paratest": "^7.0", "dmarynicz/behat-parallel-extension": "^1.0.3", "friends-of-behat/mink-browserkit-driver": "^1.5", "friends-of-behat/mink-extension": "^2.5", "friendsofphp/php-cs-fixer": "^3.14", "laracasts/behat-laravel-extension": "dev-master as 1.1.1", "larastan/larastan": "^2.0", "laravel/pint": "^1.5", "league/factory-muffin": "^3.3", "league/factory-muffin-faker": "^2.3", "league/flysystem-ziparchive": "^3.0", "mockery/mockery": "^1.4", "nunomaduro/collision": "^8.1", "onesky/api-library-php5": "dev-master", "php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpmd/phpmd": "^2.6", "phpunit/phpunit": "10.5.31", "rap2hpoutre/laravel-log-viewer": "^2.4", "spatie/laravel-ignition": "^2.4", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"classmap": ["database/migrations"], "psr-4": {"AwardForce\\": "app", "Tests\\Factory\\": "tests/Factory"}, "files": ["app/Library/helpers.php", "app/Modules/Judging/helpers.php", "app/Modules/Payments/Vendor/Bpoint/httpful.phar"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/Tests", "Tests\\Factory\\": "tests/Factory", "Features\\": "tests/Features"}}, "scripts": {"phpstan": ["./vendor/bin/phpstan analyse"], "post-install-cmd": ["php artisan clear-compiled -vvv", "bash contrib/setup_hooks.sh", "git config blame.ignoreRevsFile .git-blame-ignore-revs || true"], "post-update-cmd": ["php artisan vendor:publish --force --provider=\"Platform\\PlatformServiceProvider\"", "@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-create-project-cmd": ["php -r \"copy('.env.example', '.env');\"", "php artisan key:generate"], "ide-helper": ["php artisan clear-compiled", "php artisan ide-helper:generate -M", "php artisan ide-helper:meta -vvv", "php artisan ide-helper:models --write --reset"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "scripts-descriptions": {"phpstan": "Run PHPStan static analysis against your application."}, "config": {"discard-changes": true, "github-oauth": {}, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "bamarni/composer-bin-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}