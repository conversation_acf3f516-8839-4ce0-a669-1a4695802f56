<?php

declare(strict_types=1);

namespace AwardForce\Modules\Billing\Commands;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Billing\Contracts\UsageBillingGateway;
use AwardForce\Modules\Billing\Contracts\UsageEventPayloadFactory;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Data\UsageLogRepository;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use RuntimeException;
use Throwable;

readonly class SyncUsageLogsHandler
{
    public function __construct(
        private AccountRepository $accounts,
        private UsageEventPayloadFactory $usageEventPayloadFactory,
        private UsageBillingGateway $usageBillingGateway,
        private UsageLogRepository $usageLogRepository,
    ) {
    }

    public function handle(SyncUsageLogs $command): void
    {
        $processingId = uniqid('sync-job-', true);

        try {
            $accountIds = $this->accounts->subscriptionProvider($command->subscriptionProvider)->just('id');
            $maxEvents = $this->usageBillingGateway->maxEventsPerBatch();

            $eventsToProcess = $this->usageLogRepository->claimEventsForProcessing($processingId, $accountIds, $maxEvents);

            if ($eventsToProcess->isEmpty()) {
                return;
            }

            $this->processBatch($eventsToProcess, $command->subscriptionProvider);
        } catch (Throwable $e) {
            $this->usageLogRepository->resetBatchToReady($processingId);
            throw $e;
        }
    }

    private function processBatch(Collection $eventsToProcess, string $subscriptionProvider): void
    {
        $usageEvents = $this->createUsageEvents($eventsToProcess);

        if (mb_strlen(Json::encode($usageEvents), '8bit') > $this->usageBillingGateway->maxBatchSizeBytes()) {
            throw new RuntimeException('Batch size too large.');
        }

        $result = $this->usageBillingGateway->ingestBatch($usageEvents, $subscriptionProvider);
        $this->updateUsageLogStatuses($result);
    }

    private function createUsageEvents(Collection $events): array
    {
        return $events
            ->map(fn(UsageLog $usageLog) => $this->usageEventPayloadFactory->create($usageLog))
            ->all();
    }

    private function updateUsageLogStatuses(BatchIngestionResult $result): void
    {
        if (! empty($result->succeededIds)) {
            $this->usageLogRepository->updateStatusByIds($result->succeededIds, Status::Synced);
        }

        if (! empty($result->failedIds)) {
            defer(static fn() => Log::error('Failed to sync usage logs', $result->failedIds));
            $this->usageLogRepository->updateStatusByIds($result->failedIds, Status::Failed);
        }
    }
}
