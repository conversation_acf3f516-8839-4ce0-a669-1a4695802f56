<?php

namespace AwardForce\Modules\Billing\Commands;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Billing\Contracts\UsageBillingGateway;
use AwardForce\Modules\Billing\Contracts\UsageEventPayloadFactory;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Data\UsageLogRepository;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class SyncUsageLogsHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    protected function init(): void
    {
        UsageLog::all()->each->delete();
    }

    public function testItCanSyncUsageLogs(): void
    {
        $usageLogs = $this->muffins(3, UsageLog::class, ['status' => Status::Ready]);
        $failingUsageLog = $this->muffin(UsageLog::class, ['status' => Status::Ready]);
        UsageLog::refreshIndex();

        $usageBillingGateway = $this->mock(UsageBillingGateway::class);
        $usageBillingGateway->shouldReceive('ingestBatch')
            ->once()
            ->andReturn(new BatchIngestionResult(collect($usageLogs)->just('_id'), [$failingUsageLog->getID()]));
        $usageBillingGateway->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);
        $usageBillingGateway->shouldReceive('maxBatchSizeBytes')->once()->andReturn(490 * 1024);

        $command = new SyncUsageLogs(current_account()->subscriptionProvider);
        $handler = new SyncUsageLogsHandler(
            app(AccountRepository::class),
            app(UsageEventPayloadFactory::class),
            $usageBillingGateway,
            app(UsageLogRepository::class),
        );
        $handler->handle($command);
        UsageLog::refreshIndex();

        foreach ($usageLogs as $usageLog) {
            $this->assertEquals(Status::Synced, UsageLog::find($usageLog->getID())->status);
        }

        $this->assertEquals(Status::Failed, UsageLog::find($failingUsageLog->getID())->status);
    }

    public function testItResetsProcessingStatusOnError(): void
    {
        $readyLog = $this->muffin(UsageLog::class, ['status' => Status::Ready]);
        UsageLog::refreshIndex();

        $usageBillingGateway = $this->mock(UsageBillingGateway::class);
        $usageBillingGateway->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);
        $usageBillingGateway->shouldReceive('ingestBatch')
            ->once()
            ->andThrow(new \RuntimeException('Test error'));
        $usageBillingGateway->shouldReceive('maxBatchSizeBytes')->once()->andReturn(490 * 1024);

        $command = new SyncUsageLogs(current_account()->subscriptionProvider);
        $handler = new SyncUsageLogsHandler(
            app(AccountRepository::class),
            app(UsageEventPayloadFactory::class),
            $usageBillingGateway,
            app(UsageLogRepository::class),
        );

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Test error');

        $handler->handle($command);

        // After the exception, verify the log was reset back to Ready
        UsageLog::refreshIndex();
        $logAfter = UsageLog::find($readyLog->getID());
        $this->assertEquals(Status::Ready, $logAfter->status);
        $this->assertNull($logAfter->processingId);
    }
}
