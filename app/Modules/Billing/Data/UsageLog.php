<?php

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Library\Database\ElasticSearch\Model;
use AwardForce\Modules\Accounts\Traits\BelongsToAccount;
use AwardForce\Modules\Billing\Enums\LoggableType;
use AwardForce\Modules\Billing\Enums\Status;

/**
 * AwardForce\Modules\Usage\Models\Usage
 *
 * @property string $event
 * @property Status $status
 * @property array $metrics
 * @property array $metadata
 * @property int|null $loggableId
 * @property LoggableType|null $loggableType
 * @property int|null $accountId
 * @property int|null $userId
 * @property int $createdAt
 * @property int $updatedAt
 * @property string $processingId
 * @property-read \AwardForce\Modules\Accounts\Models\Account|null $account
 * @property-read \AwardForce\Modules\Identity\Users\Models\User|null $user
 *
 * @mixin \Eloquent
 */
class UsageLog extends Model
{
    use BelongsToAccount;

    protected $type = 'usage';
    protected $index = 'usage_logs';
    public $timestamps = false;
    protected $guarded = [];
    protected $casts = [
        'account_id' => 'integer',
        'user_id' => 'integer',
        'status' => Status::class,
        'loggable_type' => LoggableType::class,
        'loggable_id' => 'integer',
        'processing_id' => 'string',
    ];
}
