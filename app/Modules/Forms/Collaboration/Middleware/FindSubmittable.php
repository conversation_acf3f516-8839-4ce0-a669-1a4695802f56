<?php

namespace AwardForce\Modules\Forms\Collaboration\Middleware;

use AwardForce\Modules\Forms\Collaboration\Exceptions\SubmittableMissing;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use Illuminate\Http\Request;

trait FindSubmittable
{
    /**
     * @throws SubmittableMissing
     */
    protected function submittable(Request $request): Submittable
    {
        $submittable = collect($request->route()?->parameters())->first(fn($parameter) => $parameter instanceof Submittable);

        if (is_null($submittable)) {
            throw new SubmittableMissing('No submittable implementation found.');
        }

        return $submittable;
    }
}
