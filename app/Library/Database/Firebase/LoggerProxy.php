<?php

namespace AwardForce\Library\Database\Firebase;

use Eloquence\Behaviours\Slug;
use Google\Cloud\Core\Timestamp;
use Kreait\Firebase\Exception\AuthException;
use Kreait\Firebase\Exception\FirebaseException;
use Psr\Log\LoggerInterface;

final readonly class LoggerProxy implements Database
{
    public function __construct(private Firestore $firestore, private LoggerInterface $logger)
    {
    }

    public function get(Path $path): mixed
    {
        $result = $this->firestore->get($path);

        $this->logger->info('Firebase: retrieve data', ['path' => $path->toArray(), 'result' => $result]);

        return $result;
    }

    public function set(Path $path, mixed $value): void
    {
        $this->firestore->set($path, $value);

        $this->logger->info('Firebase: set data', ['path' => $path->toArray(), 'value' => $value]);
    }

    /**
     * @throws AuthException
     * @throws FirebaseException
     */
    public function generateAuthToken(Slug $userSlug, Claim $claim): string
    {
        return $this->firestore->generateAuthToken($userSlug, $claim);
    }

    public function expireAt(): Timestamp
    {
        return $this->firestore->expireAt();
    }
}
