<?php

namespace AwardForce\Http\Controllers\EntryForm;

use AwardForce\Http\Requests\Entry\Entrant\InitateRefereeReviewStageRequest;
use AwardForce\Http\Requests\Entry\Entrant\SingleElementUpdateRequest;
use AwardForce\Http\Requests\Entry\Entrant\UpdateEntryRequest;
use AwardForce\Library\Database\Firebase\Database as FirebaseDatabase;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Bus\UpdateSubmittableContributors;
use AwardForce\Modules\Notifications\Commands\SendSpecificNotificationCommand;
use AwardForce\Modules\Referees\Commands\UpdateSubmittableReferees;
use AwardForce\Modules\Referees\Models\Referee;
use AwardForce\Modules\Referees\Models\Referees;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\Seasons\Models\Season;
use Bus;
use Dingo\Api\Http\Request;
use Mockery as m;
use Platform\Kessel\Hyperdrive;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class EntrantEntryFormControllerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private EntrantEntryFormController $controller;

    public function init()
    {
        Bus::fake(SendSpecificNotificationCommand::class);
        $this->controller = app(EntrantEntryFormController::class);
        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);
        $this->spy(FirebaseDatabase::class);
    }

    public function testReferees()
    {
        $entry = $this->muffin(Entry::class);
        $refereeOne = $this->muffin(Referee::class, ['submittable_id' => $entry->id]);
        $refereeTwo = $this->muffin(Referee::class, ['submittable_id' => $entry->id]);
        $field = $this->muffin(Field::class, [
            'tab_id' => $refereeOne->id,
            'type' => 'text',
            'resource' => Field::RESOURCE_REFEREES,
        ]);
        app(ValuesService::class)->setValuesForObject([(string) $field->slug => 'value'], $refereeOne);
        $referees = $this->controller->referees($entry);

        $this->assertCount(2, $referees);
        $this->assertCount(1, $referees[$refereeOne->tabId]);
        $this->assertCount(1, $referees[$refereeTwo->tabId]);
        $this->assertContains($refereeOne->id, $referees[$refereeOne->tabId][0]);
        $this->assertContains($refereeTwo->id, $referees[$refereeTwo->tabId][0]);

        $this->assertEmpty($referees[$refereeTwo->tabId][0]['values']);
        $this->assertCount(1, $referees[$refereeOne->tabId][0]['values']);
        $this->assertEquals('value', $referees[$refereeOne->tabId][0]['values'][(string) $field->slug]);
    }

    public function testItInitiatesReviewStageForEntryReferee()
    {
        $referee = $this->muffin(Referee::class);
        $entry = $this->muffin(Entry::class);
        Membership::register(current_account(), $entry->entrant, 'en_GB');
        $reviewStage = $this->muffin(ReviewStage::class, ['review_by' => ReviewStage::REVIEW_REFEREE]);
        $referee->tab->settings = ['review-stage' => (string) $reviewStage->slug];
        $referee->tab->save();

        $entry->referees()->save($referee);

        $request = new InitateRefereeReviewStageRequest([
            'entry' => $entry->id,
            'referee' => $referee->id,
        ]);
        $response = $this->controller->initiateRefereeReviewStage($request);

        $this->assertJson($response->content());
        $this->assertInstanceOf(ReviewTask::class, $referee->refresh()->reviewTask);
        $this->assertJsonStringEqualsJsonString(json_encode(['review_task' => (string) $referee->reviewTask->token, 'request_sent_at' => $referee->requestSentAtFormatted()]), $response->content());
        Bus::assertDispatched(SendSpecificNotificationCommand::class);
    }

    public function testShouldNotInitiateReviewTaskWhenRefereeDoesNotBelongsToEntry()
    {
        $referee = $this->muffin(Referee::class);
        $entry = $this->muffin(Entry::class);
        $differentEntry = $this->muffin(Entry::class);
        Membership::register(current_account(), $entry->entrant, 'en_GB');
        Membership::register(current_account(), $differentEntry->entrant, 'en_GB');
        $reviewStage = $this->muffin(ReviewStage::class, ['review_by' => ReviewStage::REVIEW_REFEREE]);

        $entry->referees()->save($referee);

        $request = new InitateRefereeReviewStageRequest([
            'entry' => $differentEntry->id,
            'referee' => $referee->id,
        ]);

        $response = $this->controller->initiateRefereeReviewStage($request);

        $this->assertJson($response->content());
        $this->assertNull($referee->refresh()->reviewTask);
        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);
    }

    public function testShouldResendReviewTaskNotification()
    {
        $referee = $this->muffin(Referee::class);
        $entry = $this->muffin(Entry::class);
        Membership::register(current_account(), $entry->entrant, 'en_GB');
        $reviewStage = $this->muffin(ReviewStage::class, ['review_by' => ReviewStage::REVIEW_REFEREE]);
        $reviewTask = $this->muffin(ReviewTask::class, ['review_stage_id' => $reviewStage->id, 'entry_id' => $entry->id]);

        $entry->referees()->save($referee);

        $this->controller->resendRefereeReviewTask($reviewTask);

        Bus::assertDispatched(SendSpecificNotificationCommand::class);
    }

    public function testShouldUseTheRequestEntryUserIdInGetUserIdIfProvided()
    {
        $entry = $this->muffin(Entry::class);
        $request = new Request(['entry' => $entry]);

        $reflectedClass = new \ReflectionClass($this->controller);
        $getUserId = $reflectedClass->getMethod('getUserId');
        $getUserId->setAccessible(true);
        $result = $getUserId->invoke($this->controller, $request);

        $this->assertEquals($entry->userId, $result);
    }

    public function testShouldUseTheConsumerUserIdInGetUserIdIfEntryNotProvided()
    {
        $reflectedClass = new \ReflectionClass($this->controller);
        $getUserId = $reflectedClass->getMethod('getUserId');
        $getUserId->setAccessible(true);
        $result = $getUserId->invoke($this->controller, new Request());

        $this->assertEquals(consumer()->id(), $result);
    }

    public function testShouldDispatchRefereeUpdateWhenRefereeIsPresentOnTheRequest()
    {
        Bus::fake(UpdateSubmittableReferees::class);
        $entry = $this->muffin(Entry::class);

        $response = $this->controller->autosave(new UpdateEntryRequest(['entry' => $entry, 'referees' => []]));

        Bus::assertDispatched(UpdateSubmittableReferees::class);
        $this->assertEmpty($response['referees']);

    }

    public function testShouldNotDispatchRefereeUpdateWhenRefereeIsNotPresentOnTheRequest()
    {
        Bus::fake(UpdateSubmittableReferees::class);
        $entry = $this->muffin(Entry::class);

        $response = $this->controller->autosave(new UpdateEntryRequest(['entry' => $entry])); // no referees, should not dispatch the command

        Bus::assertNotDispatched(UpdateSubmittableReferees::class);
        $this->assertEquals([], $response['referees']); // empty because entry does not have any referee and command wasn't dispatched
    }

    public function testShouldDispatchContributorsUpdateWhenRefereeIsPresentOnTheRequest()
    {
        Bus::fake(UpdateSubmittableContributors::class);
        $entry = $this->muffin(Entry::class);

        $response = $this->controller->autosave(new UpdateEntryRequest(['entry' => $entry, 'contributors' => []]));

        Bus::assertDispatched(UpdateSubmittableContributors::class);
        $this->assertNull($response['contributors']);
    }

    public function testShouldNotDispatchContributorsUpdateWhenRefereeIsPresentOnTheRequest()
    {
        Bus::fake(UpdateSubmittableContributors::class);
        $entry = $this->muffin(Entry::class);

        $response = $this->controller->autosave(new UpdateEntryRequest(['entry' => $entry]));

        Bus::assertNotDispatched(UpdateSubmittableContributors::class);
        $this->assertEquals([], $response['contributors']);
    }

    public function testUpdateFieldResponsePayload()
    {
        $season = $this->muffin(Season::class);
        $entry = $this->muffin(Entry::class, ['season_id' => $season->id]);
        $field = $this->muffin(Field::class, [
            'season_id' => $season->id,
            'type' => Field::TYPE_TEXT,
            'resource' => Field::RESOURCE_FORMS,
        ]);

        $request = new SingleElementUpdateRequest([
            'entry' => $entry,
            'field' => $field,
        ]);

        $response = $this->controller->updateField($request);

        $this->assertJsonStringEqualsJsonString(json_encode(['success' => true, 'value' => null, 'updatedAt' => $entry->updatedAt->format('Y-m-d H:i:s')]), $response->content());
    }

    public function testUpdateSubmittableShouldRespondWithRefereesExcludingDeletedReviewTasks(): void
    {
        $entry = $this->muffin(Entry::class);
        Membership::register(current_account(), $entry->entrant, 'en_GB');
        $reviewStage = $this->muffin(ReviewStage::class, ['review_by' => ReviewStage::REVIEW_REFEREE]);
        $reviewTasks = $this->muffins(3, ReviewTask::class, [
            'review_stage_id' => $reviewStage->id,
            'entry_id' => $entry->id,
        ]);

        $referees = [];
        foreach ($reviewTasks as $i => $reviewTask) {
            $referees[$i] = $this->muffin(Referee::class, ['review_task_id' => $reviewTask->id, 'request_sent_at' => now(), 'request_completed_at' => now()]);
            $entry->referees()->save($referees[$i]);
        }

        $reviewTasks[0]->delete();
        $tabId = $referees[0]->tabId;

        $request = new UpdateEntryRequest([
            'entry' => $entry,
            'referees' => [
                $tabId => [
                    $referees[0]->id => [
                        'id' => $referees[0]->id,
                        'tabId' => $referees[0]->tabId,
                        'name' => $referees[0]->name,
                        'email' => $referees[0]->email,
                        'reviewTask' => (string) $reviewTasks[0]->token,
                    ],
                    $tabId => [
                        'id' => $referees[1]->id,
                        'tabId' => $referees[1]->tabId,
                        'name' => $referees[1]->name,
                        'email' => $referees[1]->email,
                        'reviewTask' => (string) $reviewTasks[1]->token,
                    ],
                ],
            ],
        ]);

        $response = $this->controller->autosave($request)['referees'];

        $this->assertCount(2, $response);
        $this->assertNotContains($referees[0]->id, array_map(fn($referee) => $referee['id'], $response));
    }
}
